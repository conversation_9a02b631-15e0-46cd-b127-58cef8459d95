import store from '../store'

const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取API基础地址
    const baseUrl = store.getters.apiBaseUrl || uni.getStorageSync('apiBaseUrl') || 'http://localhost:8080'
    
    // 构建完整URL
    let url = options.url.startsWith('http') ? options.url : baseUrl + options.url
    
    // 获取token
    const token = store.getters.token || uni.getStorageSync('token')
    
    // 设置默认请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }
    
    // 如果有token，添加到header
    if (token) {
      header['Authorization'] = 'Bearer ' + token
    }
    
    // 显示加载提示
    if (options.loading !== false) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
    }
    
    uni.request({
      url: url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      timeout: options.timeout || 10000,
      success: (res) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        // 请求成功
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data.code === 200 || res.data.success === true) {
            resolve(res.data)
          } else {
            // 业务错误
            const errorMsg = res.data.message || res.data.msg || '请求失败'
            
            // 如果是登录相关错误，清除登录状态
            if (res.data.code === 401 || res.data.code === 403) {
              store.dispatch('logout')
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }
            
            // 显示错误提示
            if (options.showError !== false) {
              uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 2000
              })
            }
            
            reject(new Error(errorMsg))
          }
        } else {
          // HTTP错误
          const errorMsg = `请求失败：${res.statusCode}`
          
          if (options.showError !== false) {
            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
          }
          
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        const errorMsg = err.errMsg || '网络错误'
        
        if (options.showError !== false) {
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
        
        reject(new Error(errorMsg))
      }
    })
  })
}

// GET请求
const get = (url, params, options = {}) => {
  return request({
    url: url,
    method: 'GET',
    data: params,
    ...options
  })
}

// POST请求
const post = (url, data, options = {}) => {
  return request({
    url: url,
    method: 'POST',
    data: data,
    ...options
  })
}

// POST表单请求
const postForm = (url, data, options = {}) => {
  return request({
    url: url,
    method: 'POST',
    data: data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options.header
    },
    ...options
  })
}

// PUT请求
const put = (url, data, options = {}) => {
  return request({
    url: url,
    method: 'PUT',
    data: data,
    ...options
  })
}

// DELETE请求
const del = (url, params, options = {}) => {
  return request({
    url: url,
    method: 'DELETE',
    data: params,
    ...options
  })
}

export {
  request,
  get,
  post,
  postForm,
  put,
  del
}

export default request