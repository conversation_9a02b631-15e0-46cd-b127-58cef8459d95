<template>
  <view class="scan-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">扫码功能</text>
      <text class="page-desc">扫码查询容器信息与解绑操作</text>
    </view>

    <!-- 扫码输入区域 -->
    <view class="scan-section">
      <view class="scan-input-group">
        <input
          class="scan-input"
          type="text"
          v-model="scanCode"
          placeholder="请扫描或输入容器编码"
          :disabled="loading"
          @confirm="handleScanSubmit"
        />
        <button class="scan-btn" @click="handleScanSubmit" :disabled="!scanCode.trim() || loading">
          {{ loading ? '查询中...' : '查询' }}
        </button>
      </view>
      
      <!-- 快捷扫码按钮 -->
      <button class="camera-btn" @click="openCamera" :disabled="loading">
        📷 打开摄像头扫码
      </button>
    </view>

    <!-- 容器信息展示 -->
    <view class="container-info" v-if="containerData">
      <text class="info-title">容器信息</text>
      <view class="info-card">
        <view class="info-row">
          <text class="info-label">容器编码：</text>
          <text class="info-value">{{ containerData.boxCode }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">容器类型：</text>
          <text class="info-value">{{ getContainerTypeName(containerData.containerType) }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">托盘容量：</text>
          <text class="info-value">{{ containerData.trayCapacity || 0 }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">任务订单：</text>
          <text class="info-value">{{ containerData.outDetailsDtoList?.[0]?.taskOrder || '无' }}</text>
        </view>
      </view>
    </view>

    <!-- 物料明细列表 -->
    <view class="material-list" v-if="containerData && containerData.outDetailsDtoList">
      <view class="list-header">
        <text class="list-title">物料明细</text>
        <text class="list-count">共{{ containerData.outDetailsDtoList.length }}项</text>
      </view>
      
      <view class="material-items">
        <view
          class="material-item"
          v-for="(item, index) in containerData.outDetailsDtoList"
          :key="item.id || index"
        >
          <view class="item-header">
            <checkbox
              :value="item.id"
              :checked="selectedItems.includes(item.id)"
              @change="onItemSelect(item.id, $event)"
              :disabled="loading"
            />
            <text class="item-title">{{ item.materialName }}</text>
          </view>
          
          <view class="item-content">
            <view class="item-row">
              <text class="item-label">物料编码：</text>
              <text class="item-value">{{ item.materialCode }}</text>
            </view>
            <view class="item-row">
              <text class="item-label">规格型号：</text>
              <text class="item-value">{{ item.specification || '无' }}</text>
            </view>
            <view class="item-row">
              <text class="item-label">库存数量：</text>
              <text class="item-value">{{ item.originalOuantity }}</text>
            </view>
            <view class="item-row">
              <text class="item-label">出库数量：</text>
              <input
                class="quantity-input"
                type="number"
                :value="item.inQuantity"
                @input="onQuantityChange(item.id, $event)"
                :disabled="!selectedItems.includes(item.id) || loading"
                placeholder="请输入出库数量"
              />
            </view>
            <view class="item-row" v-if="item.gridCode">
              <text class="item-label">格口编码：</text>
              <text class="item-value">{{ item.gridCode }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section" v-if="containerData">
      <view class="batch-actions">
        <button class="batch-btn" @click="selectAll" :disabled="loading">
          {{ isAllSelected ? '取消全选' : '全选' }}
        </button>
        <button class="batch-btn" @click="clearSelection" :disabled="loading">
          清空选择
        </button>
      </view>
      
      <button
        class="unbind-btn"
        :class="{ disabled: !canUnbind || loading }"
        @click="handleUnbind"
        :disabled="!canUnbind || loading"
      >
        {{ loading ? '解绑中...' : `解绑选中项 (${selectedItems.length})` }}
      </button>
    </view>

    <!-- 操作历史 -->
    <view class="history-section" v-if="history.length > 0">
      <text class="section-title">操作历史</text>
      <view class="history-list">
        <view
          class="history-item"
          v-for="(item, index) in history"
          :key="index"
        >
          <view class="history-header">
            <text class="history-time">{{ item.time }}</text>
            <text class="history-status" :class="item.status">
              {{ item.status === 'success' ? '成功' : '失败' }}
            </text>
          </view>
          <view class="history-content">
            <text class="history-desc">{{ item.desc }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { boxDetailsRecord, trayDetailsRecord, outBoundDetails } from '../../api'

export default {
  name: 'Scan',
  setup() {
    const state = reactive({
      loading: false,
      scanCode: '',
      containerData: null,
      selectedItems: [],
      quantityMap: {},
      history: []
    })

    const isAllSelected = computed(() => {
      if (!state.containerData?.outDetailsDtoList) return false
      return state.containerData.outDetailsDtoList.every(item => 
        state.selectedItems.includes(item.id)
      )
    })

    const canUnbind = computed(() => {
      return state.selectedItems.length > 0 && !state.loading
    })

    onMounted(() => {
      loadHistory()
    })

    const getContainerTypeName = (type) => {
      const typeMap = {
        1: '辅料容器',
        2: '机物料容器',
        'box': '料箱',
        'tray': '托盘'
      }
      return typeMap[type] || '未知类型'
    }

    const handleScanSubmit = async () => {
      if (!state.scanCode.trim()) {
        uni.showToast({
          title: '请输入容器编码',
          icon: 'none'
        })
        return
      }

      if (state.loading) return

      state.loading = true
      state.containerData = null
      state.selectedItems = []
      state.quantityMap = {}

      try {
        // 先尝试料箱查询
        let result = await boxDetailsRecord(state.scanCode.trim())
        
        // 如果料箱查询失败，尝试托盘查询
        if (!result || !result.data) {
          result = await trayDetailsRecord(state.scanCode.trim())
        }

        if (result && result.data) {
          state.containerData = result.data
          
          // 初始化数量映射
          result.data.outDetailsDtoList?.forEach(item => {
            state.quantityMap[item.id] = item.inQuantity || 0
          })

          uni.showToast({
            title: '查询成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '未找到容器信息',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('查询容器信息失败:', error)
        uni.showToast({
          title: error.message || '查询失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const openCamera = () => {
      // H5环境下的扫码实现
      // #ifdef H5
      uni.showToast({
        title: 'H5环境暂不支持摄像头扫码',
        icon: 'none'
      })
      return
      // #endif

      // App环境下的扫码实现
      // #ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          state.scanCode = res.result
          handleScanSubmit()
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
      // #endif

      // 小程序环境下的扫码实现
      // #ifdef MP
      uni.scanCode({
        success: (res) => {
          state.scanCode = res.result
          handleScanSubmit()
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
      // #endif
    }

    const onItemSelect = (itemId, event) => {
      const isChecked = event.detail.value.length > 0
      
      if (isChecked) {
        if (!state.selectedItems.includes(itemId)) {
          state.selectedItems.push(itemId)
        }
      } else {
        const index = state.selectedItems.indexOf(itemId)
        if (index > -1) {
          state.selectedItems.splice(index, 1)
        }
      }
    }

    const onQuantityChange = (itemId, event) => {
      const value = parseFloat(event.detail.value) || 0
      state.quantityMap[itemId] = value
    }

    const selectAll = () => {
      if (isAllSelected.value) {
        state.selectedItems = []
      } else {
        state.selectedItems = state.containerData.outDetailsDtoList.map(item => item.id)
      }
    }

    const clearSelection = () => {
      state.selectedItems = []
    }

    const validateUnbindData = () => {
      if (state.selectedItems.length === 0) {
        uni.showToast({
          title: '请选择要解绑的物料',
          icon: 'none'
        })
        return false
      }

      // 检查所选物料的数量
      for (const itemId of state.selectedItems) {
        const quantity = state.quantityMap[itemId]
        if (!quantity || quantity <= 0) {
          uni.showToast({
            title: '请输入有效的出库数量',
            icon: 'none'
          })
          return false
        }
      }

      return true
    }

    const handleUnbind = async () => {
      if (!validateUnbindData()) return

      // 确认操作
      const confirmResult = await new Promise((resolve) => {
        uni.showModal({
          title: '确认解绑',
          content: `确定要解绑选中的 ${state.selectedItems.length} 项物料吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!confirmResult) return

      state.loading = true

      try {
        // 构建解绑数据
        const unbindData = {
          boxNo: state.containerData.boxCode,
          machineMaterialOutBoundRecordListDTOList: state.selectedItems.map(itemId => {
            const item = state.containerData.outDetailsDtoList.find(i => i.id === itemId)
            return {
              id: itemId,
              objectId: item.objectId,
              inQuantity: state.quantityMap[itemId],
              materialCode: item.materialCode,
              materialName: item.materialName
            }
          })
        }

        const result = await outBoundDetails(unbindData)

        if (result.code === 200 || result.success === true) {
          uni.showToast({
            title: '解绑成功',
            icon: 'success'
          })

          // 添加到历史记录
          addToHistory({
            time: new Date().toLocaleString(),
            status: 'success',
            desc: `容器${state.containerData.boxCode}解绑${state.selectedItems.length}项物料`
          })

          // 重新查询容器信息
          setTimeout(() => {
            handleScanSubmit()
          }, 1000)

        }
      } catch (error) {
        console.error('解绑失败:', error)
        
        // 添加到历史记录
        addToHistory({
          time: new Date().toLocaleString(),
          status: 'error',
          desc: `解绑失败：${error.message || '未知错误'}`
        })

        uni.showToast({
          title: error.message || '解绑失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const addToHistory = (item) => {
      state.history.unshift(item)
      // 只保留最近10条记录
      if (state.history.length > 10) {
        state.history = state.history.slice(0, 10)
      }
      saveHistory()
    }

    const loadHistory = () => {
      try {
        const history = uni.getStorageSync('scan-history')
        if (history) {
          state.history = JSON.parse(history)
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    }

    const saveHistory = () => {
      try {
        uni.setStorageSync('scan-history', JSON.stringify(state.history))
      } catch (error) {
        console.error('保存历史记录失败:', error)
      }
    }

    return {
      ...state,
      isAllSelected,
      canUnbind,
      getContainerTypeName,
      handleScanSubmit,
      openCamera,
      onItemSelect,
      onQuantityChange,
      selectAll,
      clearSelection,
      handleUnbind
    }
  }
}
</script>

<style scoped>
.scan-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.scan-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.scan-input-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.scan-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
}

.scan-input:focus {
  border-color: #1890ff;
}

.scan-btn {
  width: 160rpx;
  height: 80rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-btn:disabled {
  background-color: #ccc;
}

.camera-btn {
  width: 100%;
  height: 80rpx;
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-card {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e8e8e8;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.material-list {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.list-count {
  font-size: 24rpx;
  color: #999;
}

.material-items {
  space-y: 20rpx;
}

.material-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.material-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-left: 20rpx;
}

.item-content {
  space-y: 12rpx;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.item-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.item-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.quantity-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: right;
}

.quantity-input:focus {
  border-color: #1890ff;
}

.quantity-input:disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.action-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.batch-btn {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unbind-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unbind-btn.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.history-list {
  space-y: 20rpx;
}

.history-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #e8f5e8;
  color: #52c41a;
}

.history-status.error {
  background-color: #ffe8e8;
  color: #ff4d4f;
}

.history-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.history-desc {
  word-break: break-all;
}
</style>