<template>
  <view class="merge-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">手动合并</text>
      <text class="page-desc">托盘合并操作</text>
    </view>

    <!-- 操作步骤指引 -->
    <view class="step-guide">
      <view class="step-item" :class="{ active: currentStep >= 1 }">
        <text class="step-number">1</text>
        <text class="step-text">扫描源托盘</text>
      </view>
      <view class="step-line"></view>
      <view class="step-item" :class="{ active: currentStep >= 2 }">
        <text class="step-number">2</text>
        <text class="step-text">扫描目标托盘</text>
      </view>
      <view class="step-line"></view>
      <view class="step-item" :class="{ active: currentStep >= 3 }">
        <text class="step-number">3</text>
        <text class="step-text">选择合并项</text>
      </view>
      <view class="step-line"></view>
      <view class="step-item" :class="{ active: currentStep >= 4 }">
        <text class="step-number">4</text>
        <text class="step-text">确认合并</text>
      </view>
    </view>

    <!-- 源托盘信息 -->
    <view class="tray-section">
      <text class="section-title">源托盘信息</text>
      <view class="tray-input-group">
        <input
          class="tray-input"
          type="text"
          v-model="fromTrayNo"
          placeholder="请扫描或输入源托盘编码"
          :disabled="loading || currentStep > 1"
          @confirm="loadFromTray"
        />
        <button 
          class="scan-btn" 
          @click="scanFromTray" 
          :disabled="loading || currentStep > 1"
        >
          📷 扫码
        </button>
        <button 
          class="load-btn" 
          @click="loadFromTray" 
          :disabled="!fromTrayNo.trim() || loading || currentStep > 1"
        >
          加载
        </button>
      </view>
      
      <!-- 源托盘明细 -->
      <view class="tray-details" v-if="fromTrayData">
        <view class="details-header">
          <text class="details-title">{{ fromTrayNo }}</text>
          <text class="details-count">{{ fromTrayData.length }}项物料</text>
        </view>
        <view class="material-list">
          <view
            class="material-item"
            v-for="(item, index) in fromTrayData"
            :key="index"
          >
            <view class="item-info">
              <text class="item-name">{{ item.materialName }}</text>
              <text class="item-code">{{ item.materialCode }}</text>
              <text class="item-quantity">数量: {{ item.materialQuantity }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 目标托盘信息 -->
    <view class="tray-section">
      <text class="section-title">目标托盘信息</text>
      <view class="tray-input-group">
        <input
          class="tray-input"
          type="text"
          v-model="toTrayNo"
          placeholder="请扫描或输入目标托盘编码"
          :disabled="loading || currentStep < 2 || currentStep > 2"
          @confirm="loadToTray"
        />
        <button 
          class="scan-btn" 
          @click="scanToTray" 
          :disabled="loading || currentStep < 2 || currentStep > 2"
        >
          📷 扫码
        </button>
        <button 
          class="load-btn" 
          @click="loadToTray" 
          :disabled="!toTrayNo.trim() || loading || currentStep < 2 || currentStep > 2"
        >
          加载
        </button>
      </view>
      
      <!-- 目标托盘明细 -->
      <view class="tray-details" v-if="toTrayData">
        <view class="details-header">
          <text class="details-title">{{ toTrayNo }}</text>
          <text class="details-count">{{ toTrayData.length }}项物料</text>
        </view>
        <view class="material-list">
          <view
            class="material-item"
            v-for="(item, index) in toTrayData"
            :key="index"
          >
            <view class="item-info">
              <text class="item-name">{{ item.materialName }}</text>
              <text class="item-code">{{ item.materialCode }}</text>
              <text class="item-quantity">数量: {{ item.materialQuantity }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 容量设置 -->
    <view class="volume-section" v-if="currentStep >= 3">
      <text class="section-title">容量设置</text>
      <view class="volume-inputs">
        <view class="volume-group">
          <text class="volume-label">源托盘容量</text>
          <input
            class="volume-input"
            type="number"
            v-model.number="fromVolume"
            placeholder="请输入源托盘容量"
            :disabled="loading"
          />
        </view>
        <view class="volume-group">
          <text class="volume-label">目标托盘容量</text>
          <input
            class="volume-input"
            type="number"
            v-model.number="toVolume"
            placeholder="请输入目标托盘容量"
            :disabled="loading"
          />
        </view>
      </view>
    </view>

    <!-- 合并明细选择 -->
    <view class="merge-details" v-if="currentStep >= 3 && fromTrayData">
      <text class="section-title">选择合并明细</text>
      <view class="details-actions">
        <button class="select-btn" @click="selectAll" :disabled="loading">
          {{ isAllSelected ? '取消全选' : '全选' }}
        </button>
        <button class="clear-btn" @click="clearSelection" :disabled="loading">
          清空选择
        </button>
      </view>
      
      <view class="merge-list">
        <view
          class="merge-item"
          v-for="(item, index) in fromTrayData"
          :key="index"
        >
          <view class="item-header">
            <checkbox
              :value="index"
              :checked="selectedItems.includes(index)"
              @change="onItemSelect(index, $event)"
              :disabled="loading"
            />
            <text class="item-title">{{ item.materialName }}</text>
          </view>
          
          <view class="item-details">
            <view class="detail-row">
              <text class="detail-label">物料编码：</text>
              <text class="detail-value">{{ item.materialCode }}</text>
            </view>
            <view class="detail-row" v-if="item.materialModel">
              <text class="detail-label">规格型号：</text>
              <text class="detail-value">{{ item.materialModel }}</text>
            </view>
            <view class="detail-row" v-if="item.materialColor">
              <text class="detail-label">物料颜色：</text>
              <text class="detail-value">{{ item.materialColor }}</text>
            </view>
            <view class="detail-row" v-if="item.materialSize">
              <text class="detail-label">物料尺寸：</text>
              <text class="detail-value">{{ item.materialSize }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">当前数量：</text>
              <text class="detail-value">{{ item.materialQuantity }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">合并数量：</text>
              <input
                class="quantity-input"
                type="number"
                :value="item.materialQuantity"
                @input="onQuantityChange(index, $event)"
                :disabled="!selectedItems.includes(index) || loading"
                placeholder="请输入合并数量"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 合并预览 -->
    <view class="merge-preview" v-if="currentStep >= 3 && selectedItems.length > 0">
      <text class="section-title">合并预览</text>
      <view class="preview-info">
        <view class="preview-item">
          <text class="preview-label">源托盘：</text>
          <text class="preview-value">{{ fromTrayNo }}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">目标托盘：</text>
          <text class="preview-value">{{ toTrayNo }}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">合并项数：</text>
          <text class="preview-value">{{ selectedItems.length }}项</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">总合并量：</text>
          <text class="preview-value">{{ totalMergeQuantity }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        class="action-btn secondary"
        @click="resetMerge"
        :disabled="loading"
        v-if="currentStep > 1"
      >
        重新开始
      </button>
      
      <button
        class="action-btn primary"
        :class="{ disabled: !canProceed || loading }"
        @click="handleAction"
        :disabled="!canProceed || loading"
      >
        {{ getActionText() }}
      </button>
    </view>

    <!-- 操作历史 -->
    <view class="history-section" v-if="history.length > 0">
      <text class="section-title">操作历史</text>
      <view class="history-list">
        <view
          class="history-item"
          v-for="(item, index) in history"
          :key="index"
        >
          <view class="history-header">
            <text class="history-time">{{ item.time }}</text>
            <text class="history-status" :class="item.status">
              {{ item.status === 'success' ? '成功' : '失败' }}
            </text>
          </view>
          <view class="history-content">
            <text class="history-desc">{{ item.desc }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { scanCodeGetDetails, convergeTray } from '../../api'

export default {
  name: 'Merge',
  setup() {
    const state = reactive({
      loading: false,
      currentStep: 1,
      fromTrayNo: '',
      toTrayNo: '',
      fromTrayData: null,
      toTrayData: null,
      fromVolume: null,
      toVolume: null,
      selectedItems: [],
      quantityMap: {},
      history: []
    })

    const isAllSelected = computed(() => {
      if (!state.fromTrayData) return false
      return state.fromTrayData.every((_, index) => 
        state.selectedItems.includes(index)
      )
    })

    const canProceed = computed(() => {
      switch (state.currentStep) {
        case 1:
          return state.fromTrayNo.trim() && !state.loading
        case 2:
          return state.toTrayNo.trim() && state.fromTrayData && !state.loading
        case 3:
          return state.selectedItems.length > 0 && 
                 state.fromVolume && 
                 state.toVolume && 
                 !state.loading
        default:
          return false
      }
    })

    const totalMergeQuantity = computed(() => {
      if (!state.fromTrayData || state.selectedItems.length === 0) return 0
      return state.selectedItems.reduce((sum, index) => {
        const quantity = state.quantityMap[index] || state.fromTrayData[index]?.materialQuantity || 0
        return sum + parseFloat(quantity)
      }, 0).toFixed(2)
    })

    onMounted(() => {
      loadHistory()
    })

    const scanFromTray = () => {
      scanTray((result) => {
        state.fromTrayNo = result
        loadFromTray()
      })
    }

    const scanToTray = () => {
      scanTray((result) => {
        state.toTrayNo = result
        loadToTray()
      })
    }

    const scanTray = (callback) => {
      // H5环境下的扫码实现
      // #ifdef H5
      uni.showToast({
        title: 'H5环境暂不支持摄像头扫码',
        icon: 'none'
      })
      return
      // #endif

      // App和小程序环境下的扫码实现
      uni.scanCode({
        success: (res) => {
          callback(res.result)
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    }

    const loadFromTray = async () => {
      if (!state.fromTrayNo.trim()) {
        uni.showToast({
          title: '请输入源托盘编码',
          icon: 'none'
        })
        return
      }

      state.loading = true
      try {
        const result = await scanCodeGetDetails(state.fromTrayNo.trim())
        
        if (result && Array.isArray(result) && result.length > 0) {
          state.fromTrayData = result
          state.currentStep = 2
          
          // 初始化数量映射
          result.forEach((item, index) => {
            state.quantityMap[index] = item.materialQuantity
          })

          uni.showToast({
            title: '源托盘加载成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '未找到源托盘信息',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载源托盘失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const loadToTray = async () => {
      if (!state.toTrayNo.trim()) {
        uni.showToast({
          title: '请输入目标托盘编码',
          icon: 'none'
        })
        return
      }

      if (state.fromTrayNo === state.toTrayNo) {
        uni.showToast({
          title: '源托盘和目标托盘不能相同',
          icon: 'none'
        })
        return
      }

      state.loading = true
      try {
        const result = await scanCodeGetDetails(state.toTrayNo.trim())
        
        if (result && Array.isArray(result)) {
          state.toTrayData = result
          state.currentStep = 3

          uni.showToast({
            title: '目标托盘加载成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '未找到目标托盘信息',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载目标托盘失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const onItemSelect = (index, event) => {
      const isChecked = event.detail.value.length > 0
      
      if (isChecked) {
        if (!state.selectedItems.includes(index)) {
          state.selectedItems.push(index)
        }
      } else {
        const itemIndex = state.selectedItems.indexOf(index)
        if (itemIndex > -1) {
          state.selectedItems.splice(itemIndex, 1)
        }
      }
    }

    const onQuantityChange = (index, event) => {
      const value = parseFloat(event.detail.value) || 0
      state.quantityMap[index] = value
    }

    const selectAll = () => {
      if (isAllSelected.value) {
        state.selectedItems = []
      } else {
        state.selectedItems = state.fromTrayData.map((_, index) => index)
      }
    }

    const clearSelection = () => {
      state.selectedItems = []
    }

    const getActionText = () => {
      if (state.loading) return '处理中...'
      
      switch (state.currentStep) {
        case 1:
          return '加载源托盘'
        case 2:
          return '加载目标托盘'
        case 3:
          return `确认合并 (${state.selectedItems.length}项)`
        default:
          return '下一步'
      }
    }

    const handleAction = () => {
      switch (state.currentStep) {
        case 1:
          loadFromTray()
          break
        case 2:
          loadToTray()
          break
        case 3:
          handleMerge()
          break
      }
    }

    const validateMergeData = () => {
      if (state.selectedItems.length === 0) {
        uni.showToast({
          title: '请选择要合并的物料',
          icon: 'none'
        })
        return false
      }

      if (!state.fromVolume || state.fromVolume <= 0) {
        uni.showToast({
          title: '请输入有效的源托盘容量',
          icon: 'none'
        })
        return false
      }

      if (!state.toVolume || state.toVolume <= 0) {
        uni.showToast({
          title: '请输入有效的目标托盘容量',
          icon: 'none'
        })
        return false
      }

      // 检查合并数量
      for (const index of state.selectedItems) {
        const quantity = state.quantityMap[index]
        if (!quantity || quantity <= 0) {
          uni.showToast({
            title: '请输入有效的合并数量',
            icon: 'none'
          })
          return false
        }
      }

      return true
    }

    const handleMerge = async () => {
      if (!validateMergeData()) return

      // 确认操作
      const confirmResult = await new Promise((resolve) => {
        uni.showModal({
          title: '确认合并',
          content: `确定要将 ${state.selectedItems.length} 项物料从托盘 ${state.fromTrayNo} 合并到托盘 ${state.toTrayNo} 吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!confirmResult) return

      state.loading = true

      try {
        // 构建合并数据
        const mergeData = {
          fromTrayNo: state.fromTrayNo,
          toTrayNo: state.toTrayNo,
          fromVolume: state.fromVolume,
          toVolume: state.toVolume,
          convergeTrayDetailListDTO: state.selectedItems.map(index => {
            const item = state.fromTrayData[index]
            return {
              materialCode: item.materialCode,
              materialName: item.materialName,
              materialModel: item.materialModel,
              materialColour: item.materialColor,
              materialSize: item.materialSize,
              materialQuantity: state.quantityMap[index],
              contractNo: item.contractNo,
              itemNo: item.itemNo,
              gridId: item.gridId,
              batchNo: item.batchNo,
              materialProperty: item.materialProperty
            }
          })
        }

        const result = await convergeTray(mergeData)

        if (result.code === 200 || result.success === true) {
          uni.showToast({
            title: '托盘合并成功',
            icon: 'success'
          })

          // 添加到历史记录
          addToHistory({
            time: new Date().toLocaleString(),
            status: 'success',
            desc: `${state.fromTrayNo} → ${state.toTrayNo}，合并${state.selectedItems.length}项物料`
          })

          // 重置状态
          setTimeout(() => {
            resetMerge()
          }, 1500)
        }
      } catch (error) {
        console.error('托盘合并失败:', error)
        
        // 添加到历史记录
        addToHistory({
          time: new Date().toLocaleString(),
          status: 'error',
          desc: `合并失败：${error.message || '未知错误'}`
        })

        uni.showToast({
          title: error.message || '托盘合并失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const resetMerge = () => {
      state.currentStep = 1
      state.fromTrayNo = ''
      state.toTrayNo = ''
      state.fromTrayData = null
      state.toTrayData = null
      state.fromVolume = null
      state.toVolume = null
      state.selectedItems = []
      state.quantityMap = {}
    }

    const addToHistory = (item) => {
      state.history.unshift(item)
      // 只保留最近10条记录
      if (state.history.length > 10) {
        state.history = state.history.slice(0, 10)
      }
      saveHistory()
    }

    const loadHistory = () => {
      try {
        const history = uni.getStorageSync('merge-history')
        if (history) {
          state.history = JSON.parse(history)
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    }

    const saveHistory = () => {
      try {
        uni.setStorageSync('merge-history', JSON.stringify(state.history))
      } catch (error) {
        console.error('保存历史记录失败:', error)
      }
    }

    return {
      ...state,
      isAllSelected,
      canProceed,
      totalMergeQuantity,
      scanFromTray,
      scanToTray,
      loadFromTray,
      loadToTray,
      onItemSelect,
      onQuantityChange,
      selectAll,
      clearSelection,
      getActionText,
      handleAction,
      resetMerge
    }
  }
}
</script>

<style scoped>
.merge-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.step-guide {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.step-item.active .step-number {
  background-color: #1890ff;
  color: white;
}

.step-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.step-item.active .step-text {
  color: #1890ff;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
}

.tray-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tray-input-group {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.tray-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
}

.tray-input:focus {
  border-color: #1890ff;
}

.tray-input:disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.scan-btn, .load-btn {
  width: 120rpx;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-btn {
  background-color: #52c41a;
  color: white;
}

.load-btn {
  background-color: #1890ff;
  color: white;
}

.scan-btn:disabled, .load-btn:disabled {
  background-color: #ccc;
}

.tray-details {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #f0f0f0;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.details-count {
  font-size: 24rpx;
  color: #999;
}

.material-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.material-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.material-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.item-code {
  font-size: 24rpx;
  color: #666;
}

.item-quantity {
  font-size: 24rpx;
  color: #1890ff;
  font-weight: 500;
}

.volume-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.volume-inputs {
  display: flex;
  gap: 20rpx;
}

.volume-group {
  flex: 1;
}

.volume-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.volume-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
}

.volume-input:focus {
  border-color: #1890ff;
}

.merge-details {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.details-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.select-btn, .clear-btn {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.merge-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.merge-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.merge-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-left: 20rpx;
}

.item-details {
  space-y: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
}

.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  text-align: right;
}

.quantity-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: right;
}

.quantity-input:focus {
  border-color: #1890ff;
}

.quantity-input:disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.merge-preview {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.preview-info {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e8e8e8;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-label {
  font-size: 28rpx;
  color: #666;
}

.preview-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.primary.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
}

.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.history-list {
  space-y: 20rpx;
}

.history-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #e8f5e8;
  color: #52c41a;
}

.history-status.error {
  background-color: #ffe8e8;
  color: #ff4d4f;
}

.history-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.history-desc {
  word-break: break-all;
}
</style>