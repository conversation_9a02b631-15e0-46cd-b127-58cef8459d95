<template>
  <view class="empty-box-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">呼叫空箱</text>
      <text class="page-desc">空箱出库操作</text>
    </view>

    <!-- 出库表单 -->
    <view class="form-section">
      <view class="form-group">
        <text class="form-label">出库数量</text>
        <input
          class="form-input"
          type="number"
          v-model.number="form.outNumber"
          placeholder="请输入出库数量"
          :disabled="loading"
        />
      </view>

      <view class="form-group">
        <text class="form-label">容器类型</text>
        <picker
          :range="containerTypes"
          :range-key="'label'"
          :value="selectedContainerIndex"
          @change="onContainerTypeChange"
          :disabled="loading"
        >
          <view class="picker-input">
            {{ selectedContainerType?.label || '请选择容器类型' }}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">作业任务</text>
        <picker
          :range="taskList"
          :range-key="'label'"
          :value="selectedTaskIndex"
          @change="onTaskChange"
          :disabled="loading"
        >
          <view class="picker-input">
            {{ selectedTask?.label || '请选择作业任务' }}
          </view>
        </picker>
      </view>

      <!-- 自定义任务输入 -->
      <view class="form-group" v-if="showCustomTask">
        <text class="form-label">自定义任务</text>
        <textarea
          class="form-textarea"
          v-model="form.customTask"
          placeholder="请输入自定义任务内容"
          :disabled="loading"
        />
      </view>
    </view>

    <!-- 操作信息 -->
    <view class="info-section" v-if="form.outNumber > 0">
      <text class="info-title">操作信息</text>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">出库数量：</text>
          <text class="info-value">{{ form.outNumber }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">容器类型：</text>
          <text class="info-value">{{ selectedContainerType?.label || '未选择' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">作业任务：</text>
          <text class="info-value">{{ getTaskDisplayText() }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">预计操作时间：</text>
          <text class="info-value">{{ estimatedTime }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        class="action-btn primary"
        :class="{ disabled: !canSubmit || loading }"
        @click="handleSubmit"
        :disabled="!canSubmit || loading"
      >
        {{ loading ? '处理中...' : '确认出库' }}
      </button>
      
      <button class="action-btn secondary" @click="handleReset" :disabled="loading">
        重置表单
      </button>
    </view>

    <!-- 操作历史 -->
    <view class="history-section" v-if="history.length > 0">
      <text class="section-title">操作历史</text>
      <view class="history-list">
        <view
          class="history-item"
          v-for="(item, index) in history"
          :key="index"
        >
          <view class="history-header">
            <text class="history-time">{{ item.time }}</text>
            <text class="history-status" :class="item.status">
              {{ item.status === 'success' ? '成功' : '失败' }}
            </text>
          </view>
          <view class="history-content">
            <text class="history-desc">{{ item.desc }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { addEmpty } from '../../api'

export default {
  name: 'EmptyBox',
  setup() {
    const state = reactive({
      loading: false,
      form: {
        outNumber: null,
        containerType: '',
        task: '',
        customTask: ''
      },
      selectedContainerIndex: -1,
      selectedTaskIndex: -1,
      containerTypes: [
        { label: '料箱', value: 'box' },
        { label: '托盘', value: 'tray' },
        { label: '周转箱', value: 'container' }
      ],
      taskList: [
        { label: '生产出库', value: 'production' },
        { label: '销售出库', value: 'sales' },
        { label: '调拨出库', value: 'transfer' },
        { label: '退库出库', value: 'return' },
        { label: '自定义任务', value: 'custom' }
      ],
      history: []
    })

    const selectedContainerType = computed(() => {
      return state.selectedContainerIndex >= 0 
        ? state.containerTypes[state.selectedContainerIndex] 
        : null
    })

    const selectedTask = computed(() => {
      return state.selectedTaskIndex >= 0 
        ? state.taskList[state.selectedTaskIndex] 
        : null
    })

    const showCustomTask = computed(() => {
      return selectedTask.value?.value === 'custom'
    })

    const canSubmit = computed(() => {
      return state.form.outNumber > 0 
        && selectedContainerType.value 
        && selectedTask.value
        && (!showCustomTask.value || state.form.customTask.trim())
    })

    const estimatedTime = computed(() => {
      if (!state.form.outNumber) return '0分钟'
      const baseTime = 2 // 基础时间2分钟
      const additionalTime = Math.ceil(state.form.outNumber / 10) // 每10个增加1分钟
      return `约${baseTime + additionalTime}分钟`
    })

    onMounted(() => {
      loadHistory()
    })

    const onContainerTypeChange = (e) => {
      state.selectedContainerIndex = e.detail.value
      state.form.containerType = state.containerTypes[e.detail.value].value
    }

    const onTaskChange = (e) => {
      state.selectedTaskIndex = e.detail.value
      state.form.task = state.taskList[e.detail.value].value
    }

    const getTaskDisplayText = () => {
      if (!selectedTask.value) return '未选择'
      if (selectedTask.value.value === 'custom') {
        return state.form.customTask || '自定义任务'
      }
      return selectedTask.value.label
    }

    const validateForm = () => {
      if (!state.form.outNumber || state.form.outNumber <= 0) {
        uni.showToast({
          title: '请输入有效的出库数量',
          icon: 'none'
        })
        return false
      }

      if (state.form.outNumber > 1000) {
        uni.showToast({
          title: '出库数量不能超过1000',
          icon: 'none'
        })
        return false
      }

      if (!selectedContainerType.value) {
        uni.showToast({
          title: '请选择容器类型',
          icon: 'none'
        })
        return false
      }

      if (!selectedTask.value) {
        uni.showToast({
          title: '请选择作业任务',
          icon: 'none'
        })
        return false
      }

      if (showCustomTask.value && !state.form.customTask.trim()) {
        uni.showToast({
          title: '请输入自定义任务内容',
          icon: 'none'
        })
        return false
      }

      return true
    }

    const handleSubmit = async () => {
      if (!validateForm()) return

      if (state.loading) return

      // 确认操作
      const confirmResult = await new Promise((resolve) => {
        uni.showModal({
          title: '确认出库',
          content: `确定要出库 ${state.form.outNumber} 个${selectedContainerType.value.label}吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!confirmResult) return

      state.loading = true

      try {
        // 构建请求数据
        const requestData = {
          outNumber: state.form.outNumber,
          boxContainerType: state.form.containerType,
          wmsBoxTaskList: showCustomTask.value ? state.form.customTask : selectedTask.value.label
        }

        const result = await addEmpty(requestData)

        if (result.code === 200 || result.success === true) {
          uni.showToast({
            title: '空箱出库成功',
            icon: 'success'
          })

          // 添加到历史记录
          addToHistory({
            time: new Date().toLocaleString(),
            status: 'success',
            desc: `出库${state.form.outNumber}个${selectedContainerType.value.label}，任务：${getTaskDisplayText()}`
          })

          // 重置表单
          handleReset()
        }
      } catch (error) {
        console.error('空箱出库失败:', error)
        
        // 添加到历史记录
        addToHistory({
          time: new Date().toLocaleString(),
          status: 'error',
          desc: `出库失败：${error.message || '未知错误'}`
        })

        uni.showToast({
          title: error.message || '空箱出库失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const handleReset = () => {
      state.form = {
        outNumber: null,
        containerType: '',
        task: '',
        customTask: ''
      }
      state.selectedContainerIndex = -1
      state.selectedTaskIndex = -1
    }

    const addToHistory = (item) => {
      state.history.unshift(item)
      // 只保留最近10条记录
      if (state.history.length > 10) {
        state.history = state.history.slice(0, 10)
      }
      saveHistory()
    }

    const loadHistory = () => {
      try {
        const history = uni.getStorageSync('empty-box-history')
        if (history) {
          state.history = JSON.parse(history)
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    }

    const saveHistory = () => {
      try {
        uni.setStorageSync('empty-box-history', JSON.stringify(state.history))
      } catch (error) {
        console.error('保存历史记录失败:', error)
      }
    }

    return {
      ...state,
      selectedContainerType,
      selectedTask,
      showCustomTask,
      canSubmit,
      estimatedTime,
      onContainerTypeChange,
      onTaskChange,
      getTaskDisplayText,
      handleSubmit,
      handleReset
    }
  }
}
</script>

<style scoped>
.empty-box-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1890ff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
  resize: vertical;
}

.form-textarea:focus {
  border-color: #1890ff;
}

.picker-input {
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  color: #333;
}

.info-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-list {
  space-y: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.primary.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
}

.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.history-list {
  space-y: 20rpx;
}

.history-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #e8f5e8;
  color: #52c41a;
}

.history-status.error {
  background-color: #ffe8e8;
  color: #ff4d4f;
}

.history-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.history-desc {
  word-break: break-all;
}
</style>