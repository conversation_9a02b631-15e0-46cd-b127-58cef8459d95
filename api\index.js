import { post, get, postForm } from '../utils/request'

// PDA用户登录
export const userLogin = (username, password) => {
  return postForm('/PDA/userLogin', { username, password })
}

// PDA扫码获取查看容器库存明细
export const scanCodeGetDetails = (boxNo) => {
  return postForm('/PDA/scanCodeGetDetails', { boxNo })
}

// PDA扫码获取料箱出库明细
export const boxDetailsRecord = (boxNo) => {
  return postForm('/PDA/boxDetailsRecord', { boxNo })
}

// PDA扫码获取托盘出库明细
export const trayDetailsRecord = (trayNo) => {
  return postForm('/PDA/trayDetailsRecord', { trayNo })
}

// PDA扫码解绑
export const outBoundDetails = (data) => {
  return post('/PDA/outBoundDetails', data)
}

// PDA空箱出库
export const addEmpty = (data) => {
  return post('/PDA/addempty', data)
}

// PDA合托盘
export const convergeTray = (data) => {
  return post('/PDA/convergeTray', data)
}