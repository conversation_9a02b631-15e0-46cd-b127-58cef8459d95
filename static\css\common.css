/* 公共样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  padding: 20rpx;
}

.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 32rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1890ff;
  color: white;
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-success {
  background-color: #52c41a;
  color: white;
}

.btn-success:hover {
  background-color: #73d13d;
}

.btn-warning {
  background-color: #faad14;
  color: white;
}

.btn-warning:hover {
  background-color: #ffc53d;
}

.btn-danger {
  background-color: #ff4d4f;
  color: white;
}

.btn-danger:hover {
  background-color: #ff7875;
}

.btn-block {
  width: 100%;
  display: block;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 10rpx;
  font-size: 32rpx;
  background-color: white;
}

.input:focus {
  border-color: #1890ff;
  outline: none;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.card-body {
  color: #666;
  line-height: 1.6;
}

/* 列表样式 */
.list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.list-item-desc {
  font-size: 28rpx;
  color: #999;
  margin-top: 8rpx;
}

.list-item-arrow {
  color: #999;
  font-size: 28rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: white;
}

.form-input:focus {
  border-color: #1890ff;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mt-10 {
  margin-top: 20rpx;
}

.mt-20 {
  margin-top: 40rpx;
}

.mb-10 {
  margin-bottom: 20rpx;
}

.mb-20 {
  margin-bottom: 40rpx;
}

.ml-10 {
  margin-left: 20rpx;
}

.mr-10 {
  margin-right: 20rpx;
}

.p-10 {
  padding: 20rpx;
}

.p-20 {
  padding: 40rpx;
}

/* 状态样式 */
.status-success {
  color: #52c41a;
}

.status-warning {
  color: #faad14;
}

.status-danger {
  color: #ff4d4f;
}

.status-info {
  color: #1890ff;
}

/* 加载样式 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态样式 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
}