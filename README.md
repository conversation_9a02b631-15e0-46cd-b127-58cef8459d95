# WMS PDA 应用

基于 UniApp + Vue3 开发的仓储管理系统PDA应用，提供移动端仓储操作功能。

## 项目结构

```
WMS_PDA/
├── pages/                  # 页面目录
│   ├── login/             # 登录页面
│   ├── home/              # 首页
│   ├── scan/              # 扫码功能
│   ├── container/         # 容器明细
│   ├── empty-box/         # 呼叫空箱
│   ├── merge/             # 手动合并
│   └── settings/          # 设置页面
├── components/            # 组件目录
│   ├── Loading/           # 加载组件
│   └── Toast/             # 提示组件
├── static/                # 静态资源
│   ├── css/               # 样式文件
│   ├── images/            # 图片资源
│   └── icons/             # 图标资源
├── store/                 # 状态管理
├── utils/                 # 工具函数
├── api/                   # 接口封装
├── App.vue               # 应用主组件
├── main.js               # 入口文件
├── manifest.json         # 应用配置
├── pages.json            # 页面配置
└── package.json          # 依赖配置
```

## 功能特性

### 1. 用户认证
- [x] 用户登录
- [x] 登录状态保持
- [x] 退出登录
- [x] API地址配置

### 2. 扫码功能
- [x] 扫码解绑操作
- [x] 支持摄像头扫码（App/小程序）
- [x] 手动输入容器编码
- [x] 物料明细选择
- [x] 批量操作支持

### 3. 容器管理
- [x] 容器明细查询
- [x] 库存信息展示
- [x] 物料筛选排序
- [x] 统计信息展示
- [x] 查询历史记录

### 4. 空箱管理
- [x] 呼叫空箱功能
- [x] 容器类型选择
- [x] 作业任务配置
- [x] 自定义任务支持
- [x] 操作历史记录

### 5. 托盘合并
- [x] 手动合并功能
- [x] 分步操作引导
- [x] 源/目标托盘管理
- [x] 容量设置
- [x] 合并预览

### 6. 系统设置
- [x] API地址配置
- [x] 用户信息展示
- [x] 缓存管理
- [x] 版本信息

## 技术栈

- **框架**: UniApp + Vue3
- **状态管理**: Vuex 4
- **构建工具**: Vite
- **样式**: CSS3 + 响应式设计
- **HTTP请求**: uni.request 封装

## API接口

基于后端 PDAController 实现的接口：

- `POST /PDA/userLogin` - 用户登录
- `POST /PDA/scanCodeGetDetails` - 扫码获取容器明细
- `POST /PDA/boxDetailsRecord` - 获取料箱出库明细
- `POST /PDA/trayDetailsRecord` - 获取托盘出库明细
- `POST /PDA/outBoundDetails` - 扫码解绑操作
- `POST /PDA/addempty` - 空箱出库
- `POST /PDA/convergeTray` - 托盘合并

## 安装运行

### 1. 环境准备
- Node.js 16+
- HBuilderX 或 VSCode + uni-app插件

### 2. 安装依赖
```bash
npm install
```

### 3. 开发运行
```bash
# H5端
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# App端
npm run dev:app
```

### 4. 构建打包
```bash
# H5端
npm run build:h5

# 微信小程序
npm run build:mp-weixin

# App端
npm run build:app
```

## 配置说明

### API地址配置
应用支持动态配置API地址，默认为 `http://localhost:8080`。
可在登录页面或设置页面修改API地址。

### 本地存储
应用使用 uni-app 的本地存储API保存以下数据：
- 用户登录信息
- API地址配置
- 操作历史记录

## 平台支持

- ✅ H5
- ✅ 微信小程序
- ✅ App (Android/iOS)
- ⚠️ 其他小程序平台（需要适配）

## 注意事项

1. **扫码功能**: H5端不支持摄像头扫码，建议使用App或小程序版本
2. **网络请求**: 请确保API地址配置正确并可访问
3. **权限配置**: App端需要配置相机、网络等权限
4. **跨域问题**: H5端开发时需要配置代理解决跨域

## 开发规范

### 文件命名
- 页面文件：小写+连字符，如 `empty-box.vue`
- 组件文件：大驼峰，如 `Loading.vue`
- 工具文件：小写+连字符，如 `common.js`

### 代码规范
- 使用 Vue3 Composition API
- 统一错误处理
- 响应式设计
- 组件化开发

### UI设计
- 遵循移动端设计规范
- 主色调：#1890ff
- 圆角设计：16rpx
- 间距标准：20rpx、30rpx

## 版本历史

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能模块
- 支持多平台部署

## 许可证

MIT License

## 支持

如有问题或建议，请联系开发团队。