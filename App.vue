<template>
	<view id="app">
		<view v-if="!isLogin" class="login-container">
			<Login @login-success="handleLoginSuccess" />
		</view>
		<view v-else>
			<router-view />
		</view>
	</view>
</template>

<script>
import { reactive, onMounted } from 'vue'
import Login from './pages/login/login.vue'

export default {
	name: 'App',
	components: {
		Login
	},
	setup() {
		const state = reactive({
			isLogin: false
		})

		onMounted(() => {
			// 检查登录状态
			checkLoginStatus()
		})

		const checkLoginStatus = () => {
			const token = uni.getStorageSync('token')
			const userInfo = uni.getStorageSync('userInfo')
			if (token && userInfo) {
				state.isLogin = true
			}
		}

		const handleLoginSuccess = () => {
			state.isLogin = true
		}

		return {
			...state,
			handleLoginSuccess
		}
	},
	onLaunch: function() {
		console.log('App Launch')
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	}
}
</script>

<style>
	/*每个页面公共css */
	@import './static/css/common.css';
	
	#app {
		height: 100vh;
	}
	
	.login-container {
		height: 100vh;
	}
</style>