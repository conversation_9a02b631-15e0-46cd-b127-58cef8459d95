<template>
  <view class="settings-page">
    <!-- API设置 -->
    <view class="setting-section">
      <text class="section-title">API设置</text>
      <view class="setting-item">
        <text class="setting-label">API地址</text>
        <input
          class="setting-input"
          type="text"
          v-model="apiUrl"
          placeholder="请输入API地址"
        />
        <button class="setting-btn" @click="saveApiUrl">保存</button>
      </view>
      <text class="setting-tip">
        当前API地址：{{ currentApiUrl }}
      </text>
    </view>

    <!-- 用户信息 -->
    <view class="setting-section">
      <text class="section-title">用户信息</text>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">用户名</text>
          <text class="info-value">{{ userInfo?.username || '未知' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">登录时间</text>
          <text class="info-value">{{ loginTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">应用版本</text>
          <text class="info-value">v{{ version }}</text>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="setting-section">
      <text class="section-title">应用设置</text>
      <view class="setting-list">
        <view class="setting-item-row" @click="clearCache">
          <text class="item-title">清除缓存</text>
          <text class="item-desc">清除应用缓存数据</text>
          <text class="item-arrow">></text>
        </view>
        <view class="setting-item-row" @click="checkUpdate">
          <text class="item-title">检查更新</text>
          <text class="item-desc">检查应用更新</text>
          <text class="item-arrow">></text>
        </view>
        <view class="setting-item-row" @click="showAbout">
          <text class="item-title">关于应用</text>
          <text class="item-desc">查看应用信息</text>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Settings',
  setup() {
    const store = useStore()

    const state = reactive({
      apiUrl: '',
      version: '1.0.0'
    })

    const userInfo = computed(() => store.getters.userInfo)
    const currentApiUrl = computed(() => store.getters.apiBaseUrl || 'http://localhost:8080')
    
    const loginTime = computed(() => {
      if (userInfo.value?.loginTime) {
        return new Date(userInfo.value.loginTime).toLocaleString()
      }
      return '未知'
    })

    onMounted(() => {
      state.apiUrl = currentApiUrl.value
    })

    const saveApiUrl = () => {
      if (!state.apiUrl.trim()) {
        uni.showToast({
          title: '请输入API地址',
          icon: 'none'
        })
        return
      }

      if (!state.apiUrl.startsWith('http://') && !state.apiUrl.startsWith('https://')) {
        uni.showToast({
          title: 'API地址格式不正确',
          icon: 'none'
        })
        return
      }

      const url = state.apiUrl.replace(/\/$/, '')
      store.dispatch('setApiBaseUrl', url)

      uni.showToast({
        title: 'API地址保存成功',
        icon: 'success'
      })
    }

    const clearCache = () => {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除应用缓存吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.clearStorageSync()
              store.dispatch('logout')
              uni.showToast({
                title: '缓存清除成功',
                icon: 'success'
              })
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/login/login'
                })
              }, 1000)
            } catch (error) {
              uni.showToast({
                title: '清除缓存失败',
                icon: 'none'
              })
            }
          }
        }
      })
    }

    const checkUpdate = () => {
      uni.showToast({
        title: '当前已是最新版本',
        icon: 'none'
      })
    }

    const showAbout = () => {
      uni.showModal({
        title: '关于WMS PDA',
        content: `版本：v${state.version}\n\n仓储管理系统PDA应用，提供扫码、查询、出库、合并等功能。\n\n© 2024 WMS Team`,
        showCancel: false,
        confirmText: '知道了'
      })
    }

    const handleLogout = () => {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            store.dispatch('logout')
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        }
      })
    }

    return {
      ...state,
      userInfo,
      currentApiUrl,
      loginTime,
      saveApiUrl,
      clearCache,
      checkUpdate,
      showAbout,
      handleLogout
    }
  }
}
</script>

<style scoped>
.settings-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.setting-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.setting-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #333;
}

.setting-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.setting-input:focus {
  border-color: #1890ff;
}

.setting-btn {
  width: 120rpx;
  height: 72rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setting-tip {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.info-list {
  space-y: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.setting-list {
  space-y: 0;
}

.setting-item-row {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.setting-item-row:last-child {
  border-bottom: none;
}

.setting-item-row:active {
  background-color: #f8f8f8;
}

.item-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.item-desc {
  flex: 1;
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.item-arrow {
  color: #999;
  font-size: 28rpx;
}

.logout-section {
  margin-top: 40rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>