<template>
  <view class="login-page">
    <view class="login-container">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="app-name">WMS PDA</text>
        <text class="app-desc">仓储管理系统PDA应用</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <view class="form-group">
          <text class="form-label">用户名</text>
          <input
            class="form-input"
            type="text"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            :disabled="loading"
          />
        </view>

        <view class="form-group">
          <text class="form-label">密码</text>
          <input
            class="form-input"
            type="password"
            v-model="loginForm.password"
            placeholder="请输入密码"
            :disabled="loading"
          />
        </view>

        <button
          class="login-btn"
          :class="{ disabled: loading }"
          @click="handleLogin"
          :disabled="loading"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </view>

      <!-- 设置API地址 -->
      <view class="api-setting">
        <text class="setting-label">API地址设置</text>
        <view class="setting-row">
          <input
            class="setting-input"
            type="text"
            v-model="apiUrl"
            placeholder="请输入API地址"
            :disabled="loading"
          />
          <button class="setting-btn" @click="saveApiUrl" :disabled="loading">
            保存
          </button>
        </view>
        <text class="setting-tip">
          当前API地址：{{ currentApiUrl }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { userLogin } from '../../api'

export default {
  name: 'Login',
  emits: ['login-success'],
  setup(props, { emit }) {
    const store = useStore()

    const state = reactive({
      loading: false,
      loginForm: {
        username: '',
        password: ''
      },
      apiUrl: ''
    })

    const currentApiUrl = computed(() => {
      return store.getters.apiBaseUrl || 'http://localhost:8080'
    })

    onMounted(() => {
      // 初始化API地址
      state.apiUrl = store.getters.apiBaseUrl || 'http://localhost:8080'
      
      // 从存储中读取上次登录的用户名
      const lastUsername = uni.getStorageSync('lastUsername')
      if (lastUsername) {
        state.loginForm.username = lastUsername
      }
    })

    const validateForm = () => {
      if (!state.loginForm.username.trim()) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        })
        return false
      }

      if (!state.loginForm.password.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return false
      }

      return true
    }

    const handleLogin = async () => {
      if (!validateForm()) return

      if (state.loading) return

      state.loading = true

      try {
        const result = await userLogin(
          state.loginForm.username,
          state.loginForm.password
        )

        if (result.code === 200 || result.success === true) {
          // 保存用户名用于下次登录
          uni.setStorageSync('lastUsername', state.loginForm.username)

          // 保存登录状态
          await store.dispatch('login', {
            token: result.token || 'mock-token',
            userInfo: {
              username: state.loginForm.username,
              loginTime: new Date().toISOString()
            }
          })

          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 跳转到首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/home/<USER>'
            })
            emit('login-success')
          }, 1000)
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const saveApiUrl = () => {
      if (!state.apiUrl.trim()) {
        uni.showToast({
          title: '请输入API地址',
          icon: 'none'
        })
        return
      }

      // 简单的URL格式验证
      if (!state.apiUrl.startsWith('http://') && !state.apiUrl.startsWith('https://')) {
        uni.showToast({
          title: 'API地址格式不正确',
          icon: 'none'
        })
        return
      }

      // 去除末尾的斜杠
      const url = state.apiUrl.replace(/\/$/, '')

      store.dispatch('setApiBaseUrl', url)

      uni.showToast({
        title: 'API地址保存成功',
        icon: 'success'
      })
    }

    return {
      ...state,
      currentApiUrl,
      handleLogin,
      saveApiUrl
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.login-container {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.login-form {
  margin-bottom: 40rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1890ff;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 36rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.login-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
}

.api-setting {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 40rpx;
}

.setting-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.setting-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.setting-input:focus {
  border-color: #1890ff;
}

.setting-btn {
  width: 120rpx;
  height: 72rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setting-tip {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}
</style>