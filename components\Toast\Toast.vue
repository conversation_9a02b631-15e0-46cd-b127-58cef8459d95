<template>
  <view class="toast-container" v-if="show">
    <view class="toast-content" :class="type">
      <text class="toast-icon" v-if="icon">{{ icon }}</text>
      <text class="toast-text">{{ message }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Toast',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info' // success, error, warning, info
    },
    duration: {
      type: Number,
      default: 2000
    }
  },
  computed: {
    icon() {
      const iconMap = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
      }
      return iconMap[this.type] || ''
    }
  },
  watch: {
    show(newVal) {
      if (newVal && this.duration > 0) {
        setTimeout(() => {
          this.$emit('hide')
        }, this.duration)
      }
    }
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9998;
  pointer-events: none;
}

.toast-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 12rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  max-width: 500rpx;
  animation: fadeInOut 0.3s ease-in-out;
}

.toast-content.success {
  background: rgba(82, 196, 26, 0.9);
}

.toast-content.error {
  background: rgba(255, 77, 79, 0.9);
}

.toast-content.warning {
  background: rgba(250, 173, 20, 0.9);
}

.toast-content.info {
  background: rgba(24, 144, 255, 0.9);
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.toast-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.toast-text {
  font-size: 28rpx;
  line-height: 1.4;
}
</style>