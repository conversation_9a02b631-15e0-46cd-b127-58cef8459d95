{"name": "wms-pda", "version": "1.0.0", "description": "WMS仓储管理系统PDA应用", "main": "main.js", "scripts": {"dev:app": "uni -p app", "dev:mp-weixin": "uni -p mp-weixin", "dev:h5": "uni -p h5", "build:app": "uni build -p app", "build:mp-weixin": "uni build -p mp-weixin", "build:h5": "uni build -p h5"}, "keywords": ["uni-app", "vue3", "wms", "pda"], "author": "", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-components": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-h5": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-3081220230802001", "vue": "^3.2.31", "vuex": "^4.0.2"}, "devDependencies": {"@dcloudio/types": "^3.0.0", "@dcloudio/uni-cli-shared": "3.0.0-alpha-3081220230802001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3081220230802001", "vite": "^4.0.0"}}