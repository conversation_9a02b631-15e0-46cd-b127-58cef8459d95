<template>
  <view class="home-page">
    <!-- 顶部用户信息 -->
    <view class="user-header">
      <view class="user-info">
        <text class="welcome">欢迎回来</text>
        <text class="username">{{ userInfo?.username || '用户' }}</text>
      </view>
      <button class="logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-grid">
      <view
        class="menu-item"
        v-for="menu in menuList"
        :key="menu.id"
        @click="navigateTo(menu.path)"
      >
        <view class="menu-icon">
          <text class="icon">{{ menu.icon }}</text>
        </view>
        <text class="menu-title">{{ menu.title }}</text>
        <text class="menu-desc">{{ menu.desc }}</text>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <text class="section-title">快捷操作</text>
      <view class="action-list">
        <button class="action-btn primary" @click="quickScan">
          🔍 快速扫码
        </button>
        <button class="action-btn success" @click="quickEmptyBox">
          📦 呼叫空箱
        </button>
      </view>
    </view>

    <!-- 系统信息 -->
    <view class="system-info">
      <text class="info-title">系统信息</text>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">API地址：</text>
          <text class="info-value">{{ apiBaseUrl }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">版本号：</text>
          <text class="info-value">v{{ version }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">登录时间：</text>
          <text class="info-value">{{ loginTime }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Home',
  setup() {
    const store = useStore()

    const state = reactive({
      version: '1.0.0',
      menuList: [
        {
          id: 1,
          title: '扫码功能',
          desc: '扫码查询容器信息',
          icon: '📱',
          path: '/pages/scan/scan'
        },
        {
          id: 2,
          title: '容器明细',
          desc: '查看容器库存明细',
          icon: '📋',
          path: '/pages/container/container'
        },
        {
          id: 3,
          title: '呼叫空箱',
          desc: '空箱出库操作',
          icon: '📦',
          path: '/pages/empty-box/empty-box'
        },
        {
          id: 4,
          title: '手动合并',
          desc: '托盘合并操作',
          icon: '🔄',
          path: '/pages/merge/merge'
        },
        {
          id: 5,
          title: '系统设置',
          desc: '应用设置和配置',
          icon: '⚙️',
          path: '/pages/settings/settings'
        }
      ]
    })

    const userInfo = computed(() => store.getters.userInfo)
    const apiBaseUrl = computed(() => store.getters.apiBaseUrl || 'http://localhost:8080')
    
    const loginTime = computed(() => {
      if (userInfo.value?.loginTime) {
        return new Date(userInfo.value.loginTime).toLocaleString()
      }
      return '未知'
    })

    onMounted(() => {
      // 检查登录状态
      if (!store.getters.isLogin) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    })

    const navigateTo = (path) => {
      uni.navigateTo({
        url: path
      })
    }

    const quickScan = () => {
      uni.navigateTo({
        url: '/pages/scan/scan'
      })
    }

    const quickEmptyBox = () => {
      uni.navigateTo({
        url: '/pages/empty-box/empty-box'
      })
    }

    const handleLogout = () => {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            store.dispatch('logout')
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        }
      })
    }

    return {
      ...state,
      userInfo,
      apiBaseUrl,
      loginTime,
      navigateTo,
      quickScan,
      quickEmptyBox,
      handleLogout
    }
  }
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.user-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.user-info {
  flex: 1;
}

.welcome {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
}

.logout-btn {
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 32rpx;
  font-size: 28rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.menu-item {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.menu-item:active {
  transform: scale(0.95);
}

.menu-icon {
  margin-bottom: 20rpx;
}

.icon {
  font-size: 60rpx;
}

.menu-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.menu-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.quick-actions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.action-list {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.success {
  background-color: #52c41a;
  color: white;
}

.system-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-list {
  space-y: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
</style>