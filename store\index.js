import { createStore } from 'vuex'

const store = createStore({
  state: {
    user: {
      token: '',
      userInfo: null,
      isLogin: false
    },
    app: {
      apiBaseUrl: '',
      version: '1.0.0'
    }
  },
  
  mutations: {
    SET_TOKEN(state, token) {
      state.user.token = token
      uni.setStorageSync('token', token)
    },
    
    SET_USER_INFO(state, userInfo) {
      state.user.userInfo = userInfo
      state.user.isLogin = true
      uni.setStorageSync('userInfo', userInfo)
    },
    
    SET_API_BASE_URL(state, url) {
      state.app.apiBaseUrl = url
      uni.setStorageSync('apiBaseUrl', url)
    },
    
    LOGOUT(state) {
      state.user.token = ''
      state.user.userInfo = null
      state.user.isLogin = false
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    },
    
    INIT_APP(state) {
      const token = uni.getStorageSync('token')
      const userInfo = uni.getStorageSync('userInfo')
      const apiBaseUrl = uni.getStorageSync('apiBaseUrl')
      
      if (token) {
        state.user.token = token
      }
      if (userInfo) {
        state.user.userInfo = userInfo
        state.user.isLogin = true
      }
      if (apiBaseUrl) {
        state.app.apiBaseUrl = apiBaseUrl
      }
    }
  },
  
  actions: {
    login({ commit }, { token, userInfo }) {
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', userInfo)
    },
    
    logout({ commit }) {
      commit('LOGOUT')
    },
    
    setApiBaseUrl({ commit }, url) {
      commit('SET_API_BASE_URL', url)
    },
    
    initApp({ commit }) {
      commit('INIT_APP')
    }
  },
  
  getters: {
    isLogin: state => state.user.isLogin,
    token: state => state.user.token,
    userInfo: state => state.user.userInfo,
    apiBaseUrl: state => state.app.apiBaseUrl
  }
})

export default store