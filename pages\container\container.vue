<template>
  <view class="container-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">容器明细</text>
      <text class="page-desc">查看容器库存明细信息</text>
    </view>

    <!-- 查询区域 -->
    <view class="search-section">
      <view class="search-input-group">
        <input
          class="search-input"
          type="text"
          v-model="searchCode"
          placeholder="请扫描或输入容器编码"
          :disabled="loading"
          @confirm="handleSearch"
        />
        <button class="search-btn" @click="handleSearch" :disabled="!searchCode.trim() || loading">
          {{ loading ? '查询中...' : '查询' }}
        </button>
      </view>
      
      <!-- 快捷扫码按钮 -->
      <button class="camera-btn" @click="openCamera" :disabled="loading">
        📷 扫码查询
      </button>
    </view>

    <!-- 容器基本信息 -->
    <view class="container-info" v-if="containerDetails && containerDetails.length > 0">
      <text class="info-title">容器信息</text>
      <view class="info-card">
        <view class="info-row">
          <text class="info-label">容器编码：</text>
          <text class="info-value">{{ searchCode }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">物料总数：</text>
          <text class="info-value">{{ containerDetails.length }}项</text>
        </view>
        <view class="info-row">
          <text class="info-label">总库存量：</text>
          <text class="info-value">{{ totalQuantity }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">查询时间：</text>
          <text class="info-value">{{ queryTime }}</text>
        </view>
      </view>
    </view>

    <!-- 物料明细列表 -->
    <view class="material-list" v-if="containerDetails && containerDetails.length > 0">
      <view class="list-header">
        <text class="list-title">库存明细</text>
        <view class="filter-actions">
          <button class="filter-btn" @click="toggleFilter">
            {{ showFilter ? '收起筛选' : '展开筛选' }}
          </button>
        </view>
      </view>

      <!-- 筛选条件 -->
      <view class="filter-section" v-if="showFilter">
        <view class="filter-row">
          <text class="filter-label">物料类型：</text>
          <picker
            :range="materialTypes"
            :range-key="'label'"
            :value="selectedTypeIndex"
            @change="onTypeFilterChange"
          >
            <view class="filter-picker">
              {{ selectedType?.label || '全部' }}
            </view>
          </picker>
        </view>
        <view class="filter-row">
          <text class="filter-label">排序方式：</text>
          <picker
            :range="sortOptions"
            :range-key="'label'"
            :value="selectedSortIndex"
            @change="onSortChange"
          >
            <view class="filter-picker">
              {{ selectedSort?.label || '默认排序' }}
            </view>
          </picker>
        </view>
      </view>
      
      <view class="material-items">
        <view
          class="material-item"
          v-for="(item, index) in filteredMaterials"
          :key="index"
        >
          <view class="item-header">
            <text class="item-title">{{ item.materialName }}</text>
            <text class="item-quantity">{{ item.materialQuantity }}</text>
          </view>
          
          <view class="item-content">
            <view class="item-row">
              <text class="item-label">物料编码：</text>
              <text class="item-value">{{ item.materialCode }}</text>
            </view>
            <view class="item-row" v-if="item.materialModel">
              <text class="item-label">规格型号：</text>
              <text class="item-value">{{ item.materialModel }}</text>
            </view>
            <view class="item-row" v-if="item.materialColor">
              <text class="item-label">物料颜色：</text>
              <text class="item-value">{{ item.materialColor }}</text>
            </view>
            <view class="item-row" v-if="item.materialSize">
              <text class="item-label">物料尺寸：</text>
              <text class="item-value">{{ item.materialSize }}</text>
            </view>
            <view class="item-row" v-if="item.batchNo">
              <text class="item-label">批次号：</text>
              <text class="item-value">{{ item.batchNo }}</text>
            </view>
            <view class="item-row" v-if="item.contractNo">
              <text class="item-label">合同号：</text>
              <text class="item-value">{{ item.contractNo }}</text>
            </view>
            <view class="item-row" v-if="item.itemNo">
              <text class="item-label">项目号：</text>
              <text class="item-value">{{ item.itemNo }}</text>
            </view>
            <view class="item-row" v-if="item.gridId">
              <text class="item-label">格口ID：</text>
              <text class="item-value">{{ item.gridId }}</text>
            </view>
            <view class="item-row" v-if="item.status !== undefined">
              <text class="item-label">状态：</text>
              <text class="item-value" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!loading && searched && (!containerDetails || containerDetails.length === 0)">
      <text class="empty-icon">📦</text>
      <text class="empty-text">未找到容器明细信息</text>
      <text class="empty-desc">请检查容器编码是否正确</text>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section" v-if="containerDetails && containerDetails.length > 0">
      <text class="stats-title">统计信息</text>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{ uniqueMaterialCount }}</text>
          <text class="stats-label">物料种类</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{ totalQuantity }}</text>
          <text class="stats-label">总库存量</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{ activeMaterialCount }}</text>
          <text class="stats-label">活跃物料</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{ averageQuantity }}</text>
          <text class="stats-label">平均库存</text>
        </view>
      </view>
    </view>

    <!-- 操作历史 -->
    <view class="history-section" v-if="history.length > 0">
      <text class="section-title">查询历史</text>
      <view class="history-list">
        <view
          class="history-item"
          v-for="(item, index) in history"
          :key="index"
          @click="quickSearch(item.code)"
        >
          <view class="history-content">
            <text class="history-code">{{ item.code }}</text>
            <text class="history-time">{{ item.time }}</text>
          </view>
          <text class="history-arrow">></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { scanCodeGetDetails } from '../../api'

export default {
  name: 'Container',
  setup() {
    const state = reactive({
      loading: false,
      searched: false,
      searchCode: '',
      containerDetails: null,
      queryTime: '',
      showFilter: false,
      selectedTypeIndex: -1,
      selectedSortIndex: 0,
      materialTypes: [
        { label: '全部', value: '' },
        { label: '原材料', value: 'raw' },
        { label: '半成品', value: 'semi' },
        { label: '成品', value: 'finished' },
        { label: '辅料', value: 'auxiliary' }
      ],
      sortOptions: [
        { label: '默认排序', value: 'default' },
        { label: '数量升序', value: 'quantity_asc' },
        { label: '数量降序', value: 'quantity_desc' },
        { label: '物料编码', value: 'code' },
        { label: '物料名称', value: 'name' }
      ],
      history: []
    })

    const selectedType = computed(() => {
      return state.selectedTypeIndex >= 0 
        ? state.materialTypes[state.selectedTypeIndex] 
        : null
    })

    const selectedSort = computed(() => {
      return state.sortOptions[state.selectedSortIndex]
    })

    const totalQuantity = computed(() => {
      if (!state.containerDetails) return 0
      return state.containerDetails.reduce((sum, item) => {
        return sum + (parseFloat(item.materialQuantity) || 0)
      }, 0).toFixed(2)
    })

    const uniqueMaterialCount = computed(() => {
      if (!state.containerDetails) return 0
      const codes = new Set(state.containerDetails.map(item => item.materialCode))
      return codes.size
    })

    const activeMaterialCount = computed(() => {
      if (!state.containerDetails) return 0
      return state.containerDetails.filter(item => item.status === 2).length
    })

    const averageQuantity = computed(() => {
      if (!state.containerDetails || state.containerDetails.length === 0) return 0
      return (parseFloat(totalQuantity.value) / state.containerDetails.length).toFixed(2)
    })

    const filteredMaterials = computed(() => {
      if (!state.containerDetails) return []
      
      let filtered = [...state.containerDetails]
      
      // 类型筛选
      if (selectedType.value && selectedType.value.value) {
        // 这里可以根据实际业务逻辑进行筛选
        // 暂时保留所有数据
      }
      
      // 排序
      const sortValue = selectedSort.value.value
      switch (sortValue) {
        case 'quantity_asc':
          filtered.sort((a, b) => parseFloat(a.materialQuantity) - parseFloat(b.materialQuantity))
          break
        case 'quantity_desc':
          filtered.sort((a, b) => parseFloat(b.materialQuantity) - parseFloat(a.materialQuantity))
          break
        case 'code':
          filtered.sort((a, b) => a.materialCode.localeCompare(b.materialCode))
          break
        case 'name':
          filtered.sort((a, b) => a.materialName.localeCompare(b.materialName))
          break
        default:
          // 保持原有顺序
          break
      }
      
      return filtered
    })

    onMounted(() => {
      loadHistory()
    })

    const handleSearch = async () => {
      if (!state.searchCode.trim()) {
        uni.showToast({
          title: '请输入容器编码',
          icon: 'none'
        })
        return
      }

      if (state.loading) return

      state.loading = true
      state.searched = true
      state.containerDetails = null

      try {
        const result = await scanCodeGetDetails(state.searchCode.trim())

        if (result && Array.isArray(result) && result.length > 0) {
          state.containerDetails = result
          state.queryTime = new Date().toLocaleString()

          // 添加到历史记录
          addToHistory({
            code: state.searchCode.trim(),
            time: state.queryTime
          })

          uni.showToast({
            title: '查询成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '未找到容器明细',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('查询容器明细失败:', error)
        uni.showToast({
          title: error.message || '查询失败',
          icon: 'none'
        })
      } finally {
        state.loading = false
      }
    }

    const openCamera = () => {
      // H5环境下的扫码实现
      // #ifdef H5
      uni.showToast({
        title: 'H5环境暂不支持摄像头扫码',
        icon: 'none'
      })
      return
      // #endif

      // App环境下的扫码实现
      // #ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          state.searchCode = res.result
          handleSearch()
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
      // #endif

      // 小程序环境下的扫码实现
      // #ifdef MP
      uni.scanCode({
        success: (res) => {
          state.searchCode = res.result
          handleSearch()
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
      // #endif
    }

    const toggleFilter = () => {
      state.showFilter = !state.showFilter
    }

    const onTypeFilterChange = (e) => {
      state.selectedTypeIndex = e.detail.value
    }

    const onSortChange = (e) => {
      state.selectedSortIndex = e.detail.value
    }

    const getStatusClass = (status) => {
      switch (status) {
        case 1:
          return 'status-inactive'
        case 2:
          return 'status-active'
        default:
          return 'status-unknown'
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 1:
          return '非活跃'
        case 2:
          return '活跃'
        default:
          return '未知'
      }
    }

    const quickSearch = (code) => {
      state.searchCode = code
      handleSearch()
    }

    const addToHistory = (item) => {
      // 去重，如果已存在相同编码，先移除
      const existIndex = state.history.findIndex(h => h.code === item.code)
      if (existIndex > -1) {
        state.history.splice(existIndex, 1)
      }
      
      // 添加到开头
      state.history.unshift(item)
      
      // 只保留最近10条记录
      if (state.history.length > 10) {
        state.history = state.history.slice(0, 10)
      }
      
      saveHistory()
    }

    const loadHistory = () => {
      try {
        const history = uni.getStorageSync('container-history')
        if (history) {
          state.history = JSON.parse(history)
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    }

    const saveHistory = () => {
      try {
        uni.setStorageSync('container-history', JSON.stringify(state.history))
      } catch (error) {
        console.error('保存历史记录失败:', error)
      }
    }

    return {
      ...state,
      selectedType,
      selectedSort,
      totalQuantity,
      uniqueMaterialCount,
      activeMaterialCount,
      averageQuantity,
      filteredMaterials,
      handleSearch,
      openCamera,
      toggleFilter,
      onTypeFilterChange,
      onSortChange,
      getStatusClass,
      getStatusText,
      quickSearch
    }
  }
}
</script>

<style scoped>
.container-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.search-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.search-input-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fff;
}

.search-input:focus {
  border-color: #1890ff;
}

.search-btn {
  width: 160rpx;
  height: 80rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:disabled {
  background-color: #ccc;
}

.camera-btn {
  width: 100%;
  height: 80rpx;
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-card {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e8e8e8;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.material-list {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
}

.filter-btn {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.filter-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-picker {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: white;
  display: flex;
  align-items: center;
}

.material-items {
  space-y: 20rpx;
}

.material-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.material-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.item-quantity {
  font-size: 28rpx;
  font-weight: bold;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.item-content {
  space-y: 8rpx;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6rpx 0;
}

.item-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
}

.item-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  text-align: right;
}

.status-active {
  color: #52c41a !important;
}

.status-inactive {
  color: #faad14 !important;
}

.status-unknown {
  color: #999 !important;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #999;
}

.stats-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.stats-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stats-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.history-list {
  space-y: 16rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
}

.history-item:active {
  background-color: #e8f5e8;
}

.history-content {
  flex: 1;
}

.history-code {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.history-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.history-arrow {
  color: #999;
  font-size: 28rpx;
}
</style>